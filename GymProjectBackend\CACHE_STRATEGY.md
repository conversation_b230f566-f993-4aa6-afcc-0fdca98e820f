# GymKod Cache Strategy Documentation

## 🎯 **CACHE STRATEJİSİ**

### **Hot Data (5 dakika - 300 saniye)**
Çok sık çağrılan, real-time olması gereken veriler:
- Dashboard verileri
- Aktif üye listeleri
- Kullanıcı profil bilgileri
- G<PERSON><PERSON>l istatistikler

### **Warm Data (30 dakika - 1800 saniye)**
Orta sıklıkta çağrılan, biraz gecikme tolere edilebilen veriler:
- Sayfalanmış listeler
- Arama sonuçları
- Şirket ayarları
- Üye detay bilgileri

### **Cold Data (24 saat - 86400 saniye)**
Na<PERSON>en değişen, uzun süre cache'lenebilen veriler:
- Şehir/İlçe listeleri
- Egzersiz kategorileri
- Sistem egzersizleri
- Lisans paketleri

### **Rate Limit Data (1-5 dakika)**
Rate limiting için kısa süreli cache:
- Login attempt kontrolü
- Register attempt kontrolü
- File upload/download limitleri

## 📊 **MANAGER CACHE DURUMU**

### ✅ **TAMAMLANAN MANAGER'LAR**

#### **AdvancedRateLimitManager**
- **CheckLoginAttempt**: [CacheAspect(60)] - 1 dakika
- **RecordFailedLogin**: [CacheRemoveAspect("gym:*:ratelimit:login:*")]
- **RecordSuccessfulLogin**: [CacheRemoveAspect("gym:*:ratelimit:login:*")]
- **GetRemainingLoginBanTime**: [CacheAspect(30)] - 30 saniye
- **CheckRegisterAttempt**: [CacheAspect(60)] - 1 dakika
- **RecordSuccessfulRegister**: [CacheRemoveAspect("gym:*:ratelimit:register:*")]
- **GetRemainingRegisterBanTime**: [CacheAspect(30)] - 30 saniye
- **CheckProfileImageUploadAttempt**: [CacheAspect(300)] - 5 dakika
- **RecordProfileImageUpload**: [CacheRemoveAspect("gym:*:ratelimit:profileimage:*")]
- **GetRemainingProfileImageUploads**: [CacheAspect(300)] - 5 dakika
- **CheckFileDownloadAttempt**: [CacheAspect(300)] - 5 dakika
- **RecordFileDownload**: [CacheRemoveAspect("gym:*:ratelimit:filedownload:*")]
- **GetRemainingFileDownloads**: [CacheAspect(300)] - 5 dakika

**Entegrasyon**: MemoryCache'den Redis'e geçiş yapıldı, ICacheService dependency eklendi.

#### **AuthManager**
- **UserExists**: [CacheAspect(300)] - 5 dakika (Email kontrolü)
- **CreateAccessToken**: [CacheAspect(900)] - 15 dakika (Token oluşturma)
- **CreateAccessTokenWithRefreshToken**: [CacheAspect(300)] - 5 dakika (Refresh token)
- **RevokeRefreshToken**: [CacheRemoveAspect("gym:*:auth:token:*", "gym:*:userdevice:*")]
- **RevokeAllDevices**: [CacheRemoveAspect("gym:*:auth:token:*", "gym:*:userdevice:*")]
- **RevokeDevice**: [CacheRemoveAspect("gym:*:auth:token:*", "gym:*:userdevice:*")]
- **GetUserDevices**: [CacheAspect(1800)] - 30 dakika (User devices listesi)
- **ChangeCompany**: [CacheRemoveAspect("gym:*:auth:token:*", "gym:*:user:*")]
- **ChangePassword**: [CacheRemoveAspect("gym:*:auth:*", "gym:*:user:*")]
- **CheckPasswordChangeRequired**: [CacheAspect(300)] - 5 dakika

#### **CityManager**
- **GetAll**: [CacheAspect(86400)] - 24 saat (Cold data - şehirler nadiren değişir)

#### **TownManager**
- **GetAll**: [CacheAspect(86400)] - 24 saat (Cold data - ilçeler nadiren değişir)
- **GetByCityId**: [CacheAspect(86400)] - 24 saat (Şehir bazlı ilçeler)

#### **CompanyAdressManager**
- **GetAll**: [CacheAspect(1800)] - 30 dakika (Warm data)
- **GetCompanyAdressDetails**: [CacheAspect(1800)] - 30 dakika (Warm data)
- **Add**: [CacheRemoveAspect("gym:*:companyadress:*")]
- **Update**: [CacheRemoveAspect("gym:*:companyadress:*")]
- **Delete**: [CacheRemoveAspect("gym:*:companyadress:*")]

#### **CompanyExerciseManager**
- **GetCompanyExercises**: [CacheAspect(1800)] - 30 dakika (Warm data)
- **GetCompanyExercisesByCategory**: [CacheAspect(1800)] - 30 dakika
- **GetCompanyExercisesFiltered**: [CacheAspect(900)] - 15 dakika (Filtered results)
- **SearchCompanyExercises**: [CacheAspect(600)] - 10 dakika (Search results)
- **GetCombinedExercises**: [CacheAspect(1800)] - 30 dakika (Çok sık kullanılıyor)
- **GetCombinedExercisesByCategory**: [CacheAspect(1800)] - 30 dakika
- **GetCombinedExercisesFiltered**: [CacheAspect(900)] - 15 dakika
- **Add**: [CacheRemoveAspect("gym:*:companyexercise:*", "gym:*:combinedexercise:*")]
- **Update**: [CacheRemoveAspect("gym:*:companyexercise:*", "gym:*:combinedexercise:*")]
- **Delete**: [CacheRemoveAspect("gym:*:companyexercise:*", "gym:*:combinedexercise:*")]

### 🔄 **DEVAM EDEN MANAGER'LAR**

#### **CompanyManager** (Sıradaki)
Analiz edilecek metotlar:
- GetActiveCompanies metodu
- Company CRUD işlemleri
- Company details metotları

## 🔧 **CACHE PATTERN'LERİ**

### **Cache Key Format**
```
gym:{companyId}:{entity}:{action}:{parameters}
```

### **Cache Remove Pattern'leri**
```csharp
// Member işlemleri
[CacheRemoveAspect("gym:*:member:*", "gym:*:memberdetails")]

// Payment işlemleri  
[CacheRemoveAspect("gym:*:payment:*", "gym:*:monthlypayments")]

// Rate Limit işlemleri
[CacheRemoveAspect("gym:*:ratelimit:login:*")]
```

### **Cache Aspect Kullanım Kuralları**

1. **Read Metotları**: CacheAspect ekle
2. **CUD Metotları**: CacheRemoveAspect ekle
3. **Rate Limit**: Kısa süreli cache (30-300 saniye)
4. **Dashboard**: Hot data (300 saniye)
5. **Listeler**: Warm data (1800 saniye)
6. **Statik Data**: Cold data (86400 saniye)

## 📈 **PERFORMANS HEDEFLERİ**

- **Cache Hit Ratio**: >80%
- **Response Time**: <100ms (cached)
- **Memory Usage**: <512MB Redis
- **Multi-tenant**: Company bazlı izolasyon

## 🚀 **SONRAKI ADIMLAR**

1. AuthManager cache entegrasyonu
2. CityManager cache entegrasyonu  
3. CompanyManager cache entegrasyonu
4. MemberManager cache entegrasyonu
5. PaymentManager cache entegrasyonu

## 🔍 **TEST STRATEJİSİ**

Her manager için:
1. Cache miss test (ilk çağrı)
2. Cache hit test (ikinci çağrı)
3. Cache invalidation test (CUD sonrası)
4. Multi-tenant isolation test
5. Performance benchmark test
