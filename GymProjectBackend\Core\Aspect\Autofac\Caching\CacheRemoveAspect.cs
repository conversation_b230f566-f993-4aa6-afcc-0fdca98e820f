using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// AOP Cache Remove Aspect - CUD operasyonlarında ilgili cache'leri temizler
    /// Multi-tenant aware, pattern-based cache invalidation
    /// Performance monitoring ve invalidation logging
    /// </summary>
    public class CacheRemoveAspect : MethodInterception
    {
        private readonly string[] _patterns;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly Stopwatch _stopwatch;

        /// <summary>
        /// Cache remove aspect constructor
        /// </summary>
        /// <param name="patterns">Temizlenecek cache pattern'leri (örn: "gym:*:member:*")</param>
        public CacheRemoveAspect(params string[] patterns)
        {
            _patterns = patterns ?? throw new ArgumentNullException(nameof(patterns));
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _stopwatch = new Stopwatch();
        }

        public override void Intercept(IInvocation invocation)
        {
            var methodName = $"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}";
            
            _stopwatch.Start();

            try
            {
                // Önce method'u çalıştır
                invocation.Proceed();

                // Method başarılı olduysa cache'leri temizle
                if (IsMethodSuccessful(invocation.ReturnValue))
                {
                    InvalidateCaches(methodName);
                }

                _stopwatch.Stop();
                LogCacheInvalidation(methodName, _stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _stopwatch.Stop();
                LogCacheError(methodName, ex);
                throw;
            }
        }

        /// <summary>
        /// Method'un başarılı olup olmadığını kontrol eder
        /// </summary>
        private bool IsMethodSuccessful(object returnValue)
        {
            // IResult interface'ini kontrol et
            if (returnValue is Core.Utilities.Results.IResult result)
            {
                return result.Success;
            }

            // Diğer durumlar için true döndür (void method'lar vb.)
            return true;
        }

        /// <summary>
        /// Pattern'lere göre cache'leri temizler
        /// </summary>
        private void InvalidateCaches(string methodName)
        {
            var companyId = _companyContext.GetCompanyId();
            long totalRemoved = 0;

            foreach (var pattern in _patterns)
            {
                try
                {
                    // Pattern'i company context ile genişlet
                    var expandedPattern = ExpandPattern(pattern, companyId);
                    
                    // Cache'leri temizle
                    var removedCount = _cacheService.RemoveByPattern(expandedPattern);
                    totalRemoved += removedCount;

                    Console.WriteLine($"[CACHE REMOVE] Pattern: {expandedPattern}, Removed: {removedCount}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CACHE REMOVE ERROR] Pattern: {pattern}, Error: {ex.Message}");
                }
            }

            Console.WriteLine($"[CACHE REMOVE] Method: {methodName}, Total Removed: {totalRemoved}");
        }

        /// <summary>
        /// Pattern'i company context ile genişletir
        /// </summary>
        private string ExpandPattern(string pattern, int companyId)
        {
            // Eğer pattern zaten company ID içeriyorsa olduğu gibi döndür
            if (pattern.Contains($"gym:{companyId}:"))
            {
                return pattern;
            }

            // Eğer pattern gym:*: ile başlıyorsa company ID'yi ekle
            if (pattern.StartsWith("gym:*:"))
            {
                return pattern.Replace("gym:*:", $"gym:{companyId}:");
            }

            // Eğer pattern gym: ile başlamıyorsa başına ekle
            if (!pattern.StartsWith("gym:"))
            {
                return $"gym:{companyId}:{pattern}";
            }

            return pattern;
        }

        /// <summary>
        /// Cache invalidation logging
        /// </summary>
        private void LogCacheInvalidation(string methodName, long elapsedMs)
        {
            var logMessage = $"CACHE INVALIDATION: {methodName} | Patterns: [{string.Join(", ", _patterns)}] | Duration: {elapsedMs}ms";
            Console.WriteLine($"[CACHE] {logMessage}");
        }

        /// <summary>
        /// Cache error logging
        /// </summary>
        private void LogCacheError(string methodName, Exception ex)
        {
            var logMessage = $"CACHE INVALIDATION ERROR: {methodName} | Patterns: [{string.Join(", ", _patterns)}] | Error: {ex.Message}";
            Console.WriteLine($"[CACHE] {logMessage}");
        }
    }
}
